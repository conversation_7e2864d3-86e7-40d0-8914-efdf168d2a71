// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  password  String   // bcrypt hashed
  name      String?
  verified  <PERSON><PERSON><PERSON>  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  // Usage tracking for billing/limits
  monthlyEmailLimit   Int      @default(50)
  currentMonthEmails  Int      @default(0)
  lastUsageReset      DateTime @default(now())
  planType           String   @default("free") // "free", "pro", "enterprise"
  
  // Relations
  domains   Domain[]
  webhooks  Webhook[]
  apiKeys   ApiKey[]

  @@map("users")
}

model Webhook {
  id          String   @id @default(cuid())
  name        String   // e.g., "Production API", "Test Endpoint"
  url         String
  description String?
  active      Boolean  @default(true)
  webhookSecret String? // Optional HMAC secret for webhook signature verification
  verified    <PERSON><PERSON><PERSON>  @default(false) // Whether webhook URL has been verified
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  userId      String
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  domains     Domain[] // Domains using this as default webhook
  aliases     Alias[]  // Aliases using this specific webhook

  @@map("webhooks")
}

model Domain {
  id          String   @id @default(cuid())
  domain      String   @unique
  active      Boolean  @default(true)
  verified    Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // DNS verification workflow
  verificationStatus        VerificationStatus @default(PENDING)
  lastVerificationAttempt   DateTime?
  nextVerificationCheck     DateTime?
  verificationFailureCount  Int               @default(0)
  verificationToken         String?           // For alternative verification methods
  
  // GDPR compliance
  dataRetentionDays Int @default(30)
  
  // User ownership (required)
  userId      String
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  // Webhook relationship (required)
  webhookId   String
  webhook     Webhook  @relation(fields: [webhookId], references: [id], onDelete: Restrict)
  
  // Relations
  aliases     Alias[]
  emails      Email[]
  
  @@map("domains")
}

model Alias {
  id          String   @id @default(cuid())
  email       String   // e.g., "<EMAIL>"
  active      Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Relations
  domainId    String
  domain      Domain   @relation(fields: [domainId], references: [id], onDelete: Cascade)
  
  // Webhook relationship (required - each alias has its own webhook)
  webhookId   String
  webhook     Webhook  @relation(fields: [webhookId], references: [id], onDelete: Restrict)
  
  @@unique([email, domainId])
  @@map("aliases")
}

model Email {
  id               String          @id @default(cuid())
  messageId        String          @unique
  fromAddress      String
  toAddresses      String[]
  subject          String?
  
  // Delivery tracking
  deliveryStatus   DeliveryStatus  @default(PENDING)
  deliveryAttempts Int            @default(0)
  lastAttemptAt    DateTime?
  deliveredAt      DateTime?
  errorMessage     String?
  
  // GDPR compliance - auto-expire emails
  expiresAt        DateTime
  createdAt        DateTime        @default(now())
  updatedAt        DateTime        @updatedAt
  
  // Relations
  domainId         String
  domain           Domain          @relation(fields: [domainId], references: [id], onDelete: Cascade)
  
  @@map("emails")
}

enum DeliveryStatus {
  PENDING
  DELIVERED
  FAILED
  RETRYING
  EXPIRED
}

enum VerificationStatus {
  PENDING     // Just added, waiting for verification
  VERIFIED    // DNS verification successful
  ACTIVE      // Verified and receiving emails
  WARNING     // Verification failed, grace period active
  SUSPENDED   // Grace period expired, domain disabled
  FAILED      // Verification permanently failed
}

// Optional: Audit log for GDPR compliance
model AuditLog {
  id          String   @id @default(cuid())
  action      String   // e.g., "email_processed", "webhook_delivered", "data_deleted"
  resourceId  String?  // ID of the affected resource
  resourceType String? // e.g., "email", "domain"
  metadata    Json?    // Additional context
  ipAddress   String?
  userAgent   String?
  createdAt   DateTime @default(now())
  
  // Auto-expire audit logs for GDPR compliance
  expiresAt   DateTime
  
  @@map("audit_logs")
}

model ApiKey {
  id          String   @id @default(cuid())
  name        String   // User-friendly name for the API key
  keyHash     String   @unique // Hashed version of the API key
  keyPrefix   String   // First 8 characters for display (e.g., "ak_12345...")
  lastUsedAt  DateTime?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  userId      String
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("api_keys")
}
