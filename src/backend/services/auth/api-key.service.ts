import crypto from 'crypto'
import bcrypt from 'bcrypt'
import { prisma } from '../../lib/prisma.js'
import { logger } from '../../lib/logger.js'

export interface ApiKeyData {
  name: string
  userId: string
}

export interface ApiKeyResponse {
  id: string
  name: string
  keyPrefix: string
  createdAt: Date
  lastUsedAt: Date | null
}

export interface GeneratedApiKey extends ApiKeyResponse {
  key: string // Only returned once during generation
}

export class ApiKeyService {
  private static readonly KEY_PREFIX = 'ak_'
  private static readonly KEY_LENGTH = 32 // 32 bytes = 64 hex characters
  private static readonly SALT_ROUNDS = 12

  /**
   * Generate a new API key for a user
   */
  async generateApiKey(data: ApiKeyData): Promise<{ success: boolean; apiKey?: GeneratedApiKey; error?: string }> {
    try {
      // Generate random API key
      const keyBytes = crypto.randomBytes(this.KEY_LENGTH)
      const keyString = keyBytes.toString('hex')
      const fullKey = `${ApiKeyService.KEY_PREFIX}${keyString}`
      
      // Create display prefix (first 8 characters + ...)
      const keyPrefix = `${fullKey.substring(0, 11)}...`
      
      // Hash the key for storage
      const keyHash = await bcrypt.hash(fullKey, ApiKeyService.SALT_ROUNDS)
      
      // Store in database
      const apiKey = await prisma.apiKey.create({
        data: {
          name: data.name,
          keyHash,
          keyPrefix,
          userId: data.userId
        }
      })
      
      logger.info({ userId: data.userId, apiKeyId: apiKey.id }, 'API key generated')
      
      return {
        success: true,
        apiKey: {
          id: apiKey.id,
          name: apiKey.name,
          keyPrefix: apiKey.keyPrefix,
          createdAt: apiKey.createdAt,
          lastUsedAt: apiKey.lastUsedAt,
          key: fullKey // Only returned once
        }
      }
    } catch (error) {
      logger.error({ err: error, userId: data.userId }, 'Failed to generate API key')
      return { success: false, error: 'Failed to generate API key' }
    }
  }

  /**
   * List all API keys for a user (without the actual key values)
   */
  async listApiKeys(userId: string): Promise<{ success: boolean; apiKeys?: ApiKeyResponse[]; error?: string }> {
    try {
      const apiKeys = await prisma.apiKey.findMany({
        where: { userId },
        select: {
          id: true,
          name: true,
          keyPrefix: true,
          createdAt: true,
          lastUsedAt: true
        },
        orderBy: { createdAt: 'desc' }
      })
      
      return { success: true, apiKeys }
    } catch (error) {
      logger.error({ err: error, userId }, 'Failed to list API keys')
      return { success: false, error: 'Failed to list API keys' }
    }
  }

  /**
   * Revoke (delete) an API key
   */
  async revokeApiKey(apiKeyId: string, userId: string): Promise<{ success: boolean; error?: string }> {
    try {
      // Verify the API key belongs to the user before deleting
      const apiKey = await prisma.apiKey.findFirst({
        where: { id: apiKeyId, userId }
      })
      
      if (!apiKey) {
        return { success: false, error: 'API key not found' }
      }
      
      await prisma.apiKey.delete({
        where: { id: apiKeyId }
      })
      
      logger.info({ userId, apiKeyId }, 'API key revoked')
      return { success: true }
    } catch (error) {
      logger.error({ err: error, userId, apiKeyId }, 'Failed to revoke API key')
      return { success: false, error: 'Failed to revoke API key' }
    }
  }

  /**
   * Verify an API key and return user information
   */
  async verifyApiKey(apiKey: string): Promise<{ success: boolean; user?: { id: string; email: string }; error?: string }> {
    try {
      if (!apiKey.startsWith(ApiKeyService.KEY_PREFIX)) {
        return { success: false, error: 'Invalid API key format' }
      }
      
      // Find all API keys and check against the provided key
      const apiKeys = await prisma.apiKey.findMany({
        include: {
          user: {
            select: {
              id: true,
              email: true
            }
          }
        }
      })
      
      for (const storedKey of apiKeys) {
        const isValid = await bcrypt.compare(apiKey, storedKey.keyHash)
        if (isValid) {
          // Update last used timestamp
          await prisma.apiKey.update({
            where: { id: storedKey.id },
            data: { lastUsedAt: new Date() }
          })
          
          return {
            success: true,
            user: storedKey.user
          }
        }
      }
      
      return { success: false, error: 'Invalid API key' }
    } catch (error) {
      logger.error({ err: error }, 'Failed to verify API key')
      return { success: false, error: 'Failed to verify API key' }
    }
  }
}
