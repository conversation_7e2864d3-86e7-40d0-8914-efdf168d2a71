<template>
  <div class="px-6 py-8 mx-auto max-w-7xl lg:px-8">
    <div class="mb-8">
      <h1 class="text-2xl font-bold text-gray-900">Settings</h1>
      <p class="mt-2 text-gray-600">Manage your account settings and preferences.</p>
    </div>

    <div class="grid grid-cols-1 gap-6 lg:grid-cols-3">
      <!-- Settings Navigation -->
      <div class="lg:col-span-1">
        <nav class="space-y-1">
          <a
            href="#profile"
            @click="activeSection = 'profile'"
            :class="[
              'flex items-center px-3 py-2 text-sm font-medium rounded-l-md',
              activeSection === 'profile'
                ? 'text-blue-600 border-r-2 border-blue-600 bg-blue-50'
                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
            ]"
          >
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
            Profile
          </a>
          <a
            href="#api-keys"
            @click="activeSection = 'api-keys'"
            :class="[
              'flex items-center px-3 py-2 text-sm font-medium rounded-l-md',
              activeSection === 'api-keys'
                ? 'text-blue-600 border-r-2 border-blue-600 bg-blue-50'
                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
            ]"
          >
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m0 0a2 2 0 012 2v6a2 2 0 01-2 2H9a2 2 0 01-2-2V9a2 2 0 012-2m0 0V7a2 2 0 012-2m3 0a2 2 0 00-2-2M9 7h6m6 10v-3a7.001 7.001 0 00-9.128-6.712A13.134 13.134 0 0112 5c-4.418 0-8.418 2.282-10.72 6" />
            </svg>
            API Keys
          </a>
        </nav>
      </div>

      <!-- Settings Content -->
      <div class="lg:col-span-2">

        <!-- Profile Section -->
        <div v-if="activeSection === 'profile'" class="bg-white rounded-lg shadow">
          <div id="profile" class="p-6">
            <h2 class="mb-4 text-lg font-medium text-gray-900">Profile Information</h2>

            <div class="space-y-4">
              <div>
                <label class="block mb-2 text-sm font-medium text-gray-700">Email Address</label>
                <input
                  type="email"
                  :value="user?.email || ''"
                  disabled
                  class="w-full px-3 py-2 text-gray-500 border border-gray-300 rounded-lg cursor-not-allowed bg-gray-50"
                >
                <p class="mt-1 text-xs text-gray-500">Email address cannot be changed at this time.</p>
              </div>

              <div>
                <label class="block mb-2 text-sm font-medium text-gray-700">Display Name</label>
                <input
                  type="text"
                  v-model="displayName"
                  placeholder="Enter your display name"
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
              </div>

              <div class="pt-4">
                <button
                  type="button"
                  @click="saveChanges"
                  :disabled="saving"
                  class="px-4 py-2 text-white transition-colors bg-blue-600 rounded-lg hover:bg-blue-700 disabled:opacity-50"
                >
                  {{ saving ? 'Saving...' : 'Save Changes' }}
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- API Keys Section -->
        <div v-if="activeSection === 'api-keys'" class="bg-white rounded-lg shadow">
          <div id="api-keys" class="p-6">
            <div class="flex items-center justify-between mb-6">
              <div>
                <h2 class="text-lg font-medium text-gray-900">API Keys</h2>
                <p class="mt-1 text-sm text-gray-600">
                  Manage your API keys for programmatic access to your domains, aliases, and webhooks.
                </p>
              </div>
              <button
                @click="showCreateApiKeyModal = true"
                class="px-4 py-2 text-white transition-colors bg-blue-600 rounded-lg hover:bg-blue-700"
              >
                Generate New Key
              </button>
            </div>

            <!-- API Keys List -->
            <div v-if="apiKeys.length > 0" class="space-y-4">
              <div
                v-for="apiKey in apiKeys"
                :key="apiKey.id"
                class="flex items-center justify-between p-4 border border-gray-200 rounded-lg"
              >
                <div class="flex-1">
                  <h3 class="font-medium text-gray-900">{{ apiKey.name }}</h3>
                  <p class="text-sm text-gray-500">{{ apiKey.keyPrefix }}</p>
                  <p class="text-xs text-gray-400">
                    Created {{ formatDate(apiKey.createdAt) }}
                    <span v-if="apiKey.lastUsedAt">
                      • Last used {{ formatDate(apiKey.lastUsedAt) }}
                    </span>
                    <span v-else>
                      • Never used
                    </span>
                  </p>
                </div>
                <button
                  @click="revokeApiKey(apiKey)"
                  class="px-3 py-1 text-sm text-red-600 transition-colors border border-red-300 rounded hover:bg-red-50"
                >
                  Revoke
                </button>
              </div>
            </div>

            <!-- Empty State -->
            <div v-else class="py-12 text-center">
              <svg class="w-12 h-12 mx-auto text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m0 0a2 2 0 012 2v6a2 2 0 01-2 2H9a2 2 0 01-2-2V9a2 2 0 012-2m0 0V7a2 2 0 012-2m3 0a2 2 0 00-2-2M9 7h6" />
              </svg>
              <h3 class="mt-4 text-lg font-medium text-gray-900">No API keys</h3>
              <p class="mt-2 text-gray-600">Get started by creating your first API key.</p>
              <button
                @click="showCreateApiKeyModal = true"
                class="inline-flex items-center px-4 py-2 mt-4 text-white transition-colors bg-blue-600 rounded-lg hover:bg-blue-700"
              >
                Generate API Key
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Create API Key Modal -->
    <div v-if="showCreateApiKeyModal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div class="w-full max-w-md p-6 bg-white rounded-lg shadow-xl">
        <h3 class="mb-4 text-lg font-medium text-gray-900">Generate new API key</h3>

        <div class="mb-4">
          <label class="block mb-2 text-sm font-medium text-gray-700">Key name</label>
          <input
            v-model="newApiKeyName"
            type="text"
            placeholder="e.g., Production API, Development Key"
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
          <p class="mt-1 text-xs text-gray-500">Choose a descriptive name to identify this key.</p>
        </div>

        <div class="flex justify-end space-x-3">
          <button
            @click="showCreateApiKeyModal = false"
            class="px-4 py-2 text-gray-700 transition-colors border border-gray-300 rounded-lg hover:bg-gray-50"
          >
            Cancel
          </button>
          <button
            @click="generateApiKey"
            :disabled="!newApiKeyName.trim() || generatingKey"
            class="px-4 py-2 text-white transition-colors bg-blue-600 rounded-lg hover:bg-blue-700 disabled:opacity-50"
          >
            {{ generatingKey ? 'Generating...' : 'Generate key' }}
          </button>
        </div>
      </div>
    </div>

    <!-- API Key Generated Modal -->
    <div v-if="showApiKeyModal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div class="w-full max-w-lg p-6 bg-white rounded-lg shadow-xl">
        <div class="flex items-center mb-4">
          <svg class="w-6 h-6 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <h3 class="text-lg font-medium text-gray-900">API key generated</h3>
        </div>

        <div class="p-4 mb-4 border border-yellow-200 rounded-lg bg-yellow-50">
          <p class="text-sm font-medium text-yellow-800">Important: save this key now!</p>
          <p class="mt-1 text-sm text-yellow-700">
            This is the only time you'll see the full API key. Store it securely.
          </p>
        </div>

        <div class="mb-4">
          <label class="block mb-2 text-sm font-medium text-gray-700">Your API key</label>
          <div class="flex">
            <input
              :value="generatedApiKey"
              readonly
              class="flex-1 px-3 py-2 font-mono text-sm border border-gray-300 rounded-l-lg bg-gray-50"
            >
            <button
              @click="copyApiKey"
              class="px-3 py-2 text-white bg-blue-600 border border-blue-600 rounded-r-lg hover:bg-blue-700"
            >
              {{ copied ? 'Copied!' : 'Copy' }}
            </button>
          </div>
        </div>

        <div class="flex justify-end">
          <button
            @click="closeApiKeyModal"
            class="px-4 py-2 text-white transition-colors bg-blue-600 rounded-lg hover:bg-blue-700"
          >
            Done
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

// Props
interface Props {
  user?: {
    email: string
    name?: string
  }
}

const props = withDefaults(defineProps<Props>(), {
  user: () => ({ email: '', name: '' })
})

// Types
interface ApiKey {
  id: string
  name: string
  keyPrefix: string
  createdAt: string
  lastUsedAt: string | null
}

// State
const activeSection = ref('profile')
const displayName = ref(props.user?.name || '')
const saving = ref(false)

// API Keys state
const apiKeys = ref<ApiKey[]>([])
const showCreateApiKeyModal = ref(false)
const showApiKeyModal = ref(false)
const newApiKeyName = ref('')
const generatingKey = ref(false)
const generatedApiKey = ref('')
const copied = ref(false)

// Methods
const saveChanges = async () => {
  saving.value = true
  try {
    // TODO: Implement actual save functionality
    console.log('Saving changes:', { displayName: displayName.value })

    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))

    // Show success message (could be enhanced with toast notifications)
    alert('Changes saved successfully!')
  } catch (error) {
    console.error('Failed to save changes:', error)
    alert('Failed to save changes. Please try again.')
  } finally {
    saving.value = false
  }
}

// API Key methods
const loadApiKeys = async () => {
  try {
    const response = await fetch('/api/api-keys', {
      credentials: 'include'
    })

    if (response.ok) {
      const data = await response.json()
      apiKeys.value = data.apiKeys || []
    } else {
      console.error('Failed to load API keys')
    }
  } catch (error) {
    console.error('Error loading API keys:', error)
  }
}

const generateApiKey = async () => {
  if (!newApiKeyName.value.trim()) return

  generatingKey.value = true
  try {
    const response = await fetch('/api/api-keys', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      credentials: 'include',
      body: JSON.stringify({
        name: newApiKeyName.value.trim()
      })
    })

    if (response.ok) {
      const data = await response.json()
      generatedApiKey.value = data.apiKey.key
      showCreateApiKeyModal.value = false
      showApiKeyModal.value = true
      newApiKeyName.value = ''

      // Reload API keys list
      await loadApiKeys()
    } else {
      const error = await response.json()
      alert(`Failed to generate API key: ${error.message}`)
    }
  } catch (error) {
    console.error('Error generating API key:', error)
    alert('Failed to generate API key. Please try again.')
  } finally {
    generatingKey.value = false
  }
}

const revokeApiKey = async (apiKey: ApiKey) => {
  if (!confirm(`Are you sure you want to revoke "${apiKey.name}"? This action cannot be undone.`)) {
    return
  }

  try {
    const response = await fetch(`/api/api-keys/${apiKey.id}`, {
      method: 'DELETE',
      credentials: 'include'
    })

    if (response.ok) {
      // Remove from local list
      apiKeys.value = apiKeys.value.filter(k => k.id !== apiKey.id)
    } else {
      const error = await response.json()
      alert(`Failed to revoke API key: ${error.message}`)
    }
  } catch (error) {
    console.error('Error revoking API key:', error)
    alert('Failed to revoke API key. Please try again.')
  }
}

const copyApiKey = async () => {
  try {
    await navigator.clipboard.writeText(generatedApiKey.value)
    copied.value = true
    setTimeout(() => {
      copied.value = false
    }, 2000)
  } catch (error) {
    console.error('Failed to copy API key:', error)
    // Fallback for older browsers
    const textArea = document.createElement('textarea')
    textArea.value = generatedApiKey.value
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
    copied.value = true
    setTimeout(() => {
      copied.value = false
    }, 2000)
  }
}

const closeApiKeyModal = () => {
  showApiKeyModal.value = false
  generatedApiKey.value = ''
  copied.value = false
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

onMounted(() => {
  // Initialize display name from user prop
  displayName.value = props.user?.name || ''

  // Load API keys
  loadApiKeys()
})
</script>

<style scoped>
/* Settings page specific styles */
</style>
